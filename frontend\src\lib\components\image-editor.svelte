<script lang="ts">
  interface Props {
    imageFile: File;
    onSave: (editedFile: File) => void;
    onCancel: () => void;
    locale: "en" | "ru";
  }

  const i18n = {
    en: {
      imageEditor: "Image Editor",
      crop: "Crop",
      resize: "Resize",
      rotate: "Rotate",
      flip: "Flip",
      reset: "Reset",
      save: "Save",
      cancel: "Cancel",
      width: "Width",
      height: "Height",
      maintainAspectRatio: "Maintain aspect ratio",
      flipHorizontal: "Flip Horizontal",
      flipVertical: "Flip Vertical",
      rotateLeft: "Rotate Left",
      rotateRight: "Rotate Right",
      quality: "Quality",
      cropMode: "Crop Mode",
      resizeMode: "Resize Mode",
      rotateMode: "Rotate & Flip Mode",
      dragToSelect: "Drag to select crop area",
      processing: "Processing...",
    },
    ru: {
      imageEditor: "Редактор изображений",
      crop: "Обрезка",
      resize: "Изменить размер",
      rotate: "Поворот",
      flip: "Отражение",
      reset: "Сброс",
      save: "Сохранить",
      cancel: "Отменить",
      width: "Ширина",
      height: "Высота",
      maintainAspectRatio: "Сохранить пропорции",
      flipHorizontal: "Отразить горизонтально",
      flipVertical: "Отразить вертикально",
      rotateLeft: "Повернуть влево",
      rotateRight: "Повернуть вправо",
      quality: "Качество",
      cropMode: "Режим обрезки",
      resizeMode: "Режим изменения размера",
      rotateMode: "Режим поворота и отражения",
      dragToSelect: "Перетащите для выбора области обрезки",
      processing: "Обработка...",
    },
  };

  const { imageFile, onSave, onCancel, locale }: Props = $props();
  const t = $derived(i18n[locale]);

  // Editor state
  let canvas = $state<HTMLCanvasElement>();
  let ctx = $state<CanvasRenderingContext2D>();
  let image = $state<HTMLImageElement>();
  let imageLoaded = $state(false);
  let processing = $state(false);

  // Editor modes
  type EditorMode = "crop" | "resize" | "rotate";
  let currentMode = $state<EditorMode>("crop");

  // Image transformations
  let rotation = $state(0);
  let flipX = $state(false);
  let flipY = $state(false);
  let scale = $state(1);

  // Original image dimensions
  let originalWidth = $state(0);
  let originalHeight = $state(0);

  // Current canvas dimensions
  let canvasWidth = $state(800);
  let canvasHeight = $state(600);

  // Resize settings
  let targetWidth = $state(0);
  let targetHeight = $state(0);
  let maintainAspectRatio = $state(true);
  let quality = $state(0.9);

  // Crop settings
  let cropX = $state(0);
  let cropY = $state(0);
  let cropWidth = $state(0);
  let cropHeight = $state(0);
  let isDragging = $state(false);
  let dragStartX = $state(0);
  let dragStartY = $state(0);

  // Initialize image
  $effect(() => {
    if (imageFile) {
      loadImage();
    }
  });

  async function loadImage() {
    try {
      const img = new Image();
      const url = URL.createObjectURL(imageFile);

      img.onerror = () => {
        console.error("Failed to load image");
        URL.revokeObjectURL(url);
      };

      img.onload = () => {
        image = img;
        originalWidth = img.width;
        originalHeight = img.height;
        targetWidth = img.width;
        targetHeight = img.height;

        // Calculate canvas size to fit image while maintaining aspect ratio
        const maxCanvasWidth = 800;
        const maxCanvasHeight = 600;
        const aspectRatio = img.width / img.height;

        if (aspectRatio > maxCanvasWidth / maxCanvasHeight) {
          canvasWidth = maxCanvasWidth;
          canvasHeight = maxCanvasWidth / aspectRatio;
        } else {
          canvasHeight = maxCanvasHeight;
          canvasWidth = maxCanvasHeight * aspectRatio;
        }

        scale = canvasWidth / img.width;

        // Initialize crop to full image
        cropX = 0;
        cropY = 0;
        cropWidth = canvasWidth;
        cropHeight = canvasHeight;

        imageLoaded = true;
        URL.revokeObjectURL(url);

        // Draw image after it loads
        setTimeout(() => {
          if (canvas && ctx) {
            drawImage();
          }
        }, 100);
      };

      img.src = url;
    } catch (error) {
      console.error("Error loading image:", error);
    }
  }

  function drawImage() {
    if (!canvas || !ctx || !image) return;

    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    ctx.save();

    // Apply transformations
    ctx.translate(canvasWidth / 2, canvasHeight / 2);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.scale(flipX ? -1 : 1, flipY ? -1 : 1);

    // Draw image centered
    const drawWidth = canvasWidth;
    const drawHeight = canvasHeight;
    ctx.drawImage(image, -drawWidth / 2, -drawHeight / 2, drawWidth, drawHeight);

    ctx.restore();

    // Draw crop overlay if in crop mode
    if (currentMode === "crop") {
      drawCropOverlay();
    }
  }

  function drawCropOverlay() {
    if (!ctx) return;

    // Semi-transparent overlay
    ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // Clear crop area
    ctx.clearRect(cropX, cropY, cropWidth, cropHeight);

    // Crop border
    ctx.strokeStyle = "#007bff";
    ctx.lineWidth = 2;
    ctx.strokeRect(cropX, cropY, cropWidth, cropHeight);

    // Corner handles
    const handleSize = 8;
    ctx.fillStyle = "#007bff";

    // Top-left
    ctx.fillRect(cropX - handleSize / 2, cropY - handleSize / 2, handleSize, handleSize);
    // Top-right
    ctx.fillRect(
      cropX + cropWidth - handleSize / 2,
      cropY - handleSize / 2,
      handleSize,
      handleSize,
    );
    // Bottom-left
    ctx.fillRect(
      cropX - handleSize / 2,
      cropY + cropHeight - handleSize / 2,
      handleSize,
      handleSize,
    );
    // Bottom-right
    ctx.fillRect(
      cropX + cropWidth - handleSize / 2,
      cropY + cropHeight - handleSize / 2,
      handleSize,
      handleSize,
    );
  }

  // Canvas event handlers
  function handleMouseDown(e: MouseEvent) {
    if (currentMode !== "crop") return;

    const rect = canvas?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    isDragging = true;
    dragStartX = x;
    dragStartY = y;
    cropX = x;
    cropY = y;
    cropWidth = 0;
    cropHeight = 0;
  }

  function handleMouseMove(e: MouseEvent) {
    if (!isDragging || currentMode !== "crop") return;

    const rect = canvas?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    cropWidth = Math.abs(x - dragStartX);
    cropHeight = Math.abs(y - dragStartY);
    cropX = Math.min(x, dragStartX);
    cropY = Math.min(y, dragStartY);

    // Constrain to canvas bounds
    cropX = Math.max(0, Math.min(cropX, canvasWidth));
    cropY = Math.max(0, Math.min(cropY, canvasHeight));
    cropWidth = Math.min(cropWidth, canvasWidth - cropX);
    cropHeight = Math.min(cropHeight, canvasHeight - cropY);

    drawImage();
  }

  function handleMouseUp() {
    isDragging = false;
  }

  // Transform functions
  function rotateLeft() {
    rotation = (rotation - 90) % 360;
    drawImage();
  }

  function rotateRight() {
    rotation = (rotation + 90) % 360;
    drawImage();
  }

  function toggleFlipX() {
    flipX = !flipX;
    drawImage();
  }

  function toggleFlipY() {
    flipY = !flipY;
    drawImage();
  }

  function resetTransforms() {
    rotation = 0;
    flipX = false;
    flipY = false;
    cropX = 0;
    cropY = 0;
    cropWidth = canvasWidth;
    cropHeight = canvasHeight;
    targetWidth = originalWidth;
    targetHeight = originalHeight;
    drawImage();
  }

  // Resize handlers
  function handleWidthChange() {
    if (maintainAspectRatio && originalWidth > 0) {
      targetHeight = Math.round((targetWidth * originalHeight) / originalWidth);
    }
  }

  function handleHeightChange() {
    if (maintainAspectRatio && originalHeight > 0) {
      targetWidth = Math.round((targetHeight * originalWidth) / originalHeight);
    }
  }

  // Save edited image
  async function saveImage() {
    if (!canvas || !ctx || !image) return;

    processing = true;

    try {
      // Create a new canvas for the final image
      const outputCanvas = document.createElement("canvas");
      const outputCtx = outputCanvas.getContext("2d");
      if (!outputCtx) throw new Error("Could not get canvas context");

      let finalWidth = targetWidth;
      let finalHeight = targetHeight;

      // If in crop mode, adjust dimensions
      if (currentMode === "crop" && cropWidth > 0 && cropHeight > 0) {
        const cropScaleX = originalWidth / canvasWidth;
        const cropScaleY = originalHeight / canvasHeight;
        finalWidth = Math.round(cropWidth * cropScaleX);
        finalHeight = Math.round(cropHeight * cropScaleY);
      }

      outputCanvas.width = finalWidth;
      outputCanvas.height = finalHeight;

      outputCtx.save();

      // Apply transformations
      outputCtx.translate(finalWidth / 2, finalHeight / 2);
      outputCtx.rotate((rotation * Math.PI) / 180);
      outputCtx.scale(flipX ? -1 : 1, flipY ? -1 : 1);

      if (currentMode === "crop" && cropWidth > 0 && cropHeight > 0) {
        // Draw cropped portion
        const cropScaleX = originalWidth / canvasWidth;
        const cropScaleY = originalHeight / canvasHeight;
        const sourceCropX = cropX * cropScaleX;
        const sourceCropY = cropY * cropScaleY;
        const sourceCropWidth = cropWidth * cropScaleX;
        const sourceCropHeight = cropHeight * cropScaleY;

        outputCtx.drawImage(
          image,
          sourceCropX,
          sourceCropY,
          sourceCropWidth,
          sourceCropHeight,
          -finalWidth / 2,
          -finalHeight / 2,
          finalWidth,
          finalHeight,
        );
      } else {
        // Draw full image with resize
        outputCtx.drawImage(image, -finalWidth / 2, -finalHeight / 2, finalWidth, finalHeight);
      }

      outputCtx.restore();

      // Convert to blob and create file
      const blob = await new Promise<Blob>((resolve) => {
        outputCanvas.toBlob(
          (blob) => {
            resolve(blob!);
          },
          imageFile.type,
          quality,
        );
      });

      const editedFile = new File([blob], imageFile.name, {
        type: imageFile.type,
        lastModified: Date.now(),
      });

      onSave(editedFile);
    } catch (error) {
      console.error("Error saving image:", error);
    } finally {
      processing = false;
    }
  }

  // Initialize canvas context
  $effect(() => {
    if (canvas) {
      ctx = canvas.getContext("2d") || undefined;
    }
  });

  // Redraw image when transformations change
  $effect(() => {
    if (canvas && ctx && image && imageLoaded) {
      drawImage();
    }
  });
</script>

<div class="image-editor">
  <div class="editor-header">
    <h5 class="mb-0">{t.imageEditor}</h5>
  </div>

  <div class="editor-content">
    <!-- Mode Tabs -->
    <div class="mode-tabs mb-3">
      <button
        type="button"
        class="btn btn-sm {currentMode === 'crop' ? 'btn-primary' : 'btn-outline-primary'}"
        onclick={() => {
          currentMode = "crop";
          drawImage();
        }}
      >
        {t.crop}
      </button>
      <button
        type="button"
        class="btn btn-sm {currentMode === 'resize' ? 'btn-primary' : 'btn-outline-primary'}"
        onclick={() => {
          currentMode = "resize";
          drawImage();
        }}
      >
        {t.resize}
      </button>
      <button
        type="button"
        class="btn btn-sm {currentMode === 'rotate' ? 'btn-primary' : 'btn-outline-primary'}"
        onclick={() => {
          currentMode = "rotate";
          drawImage();
        }}
      >
        {t.rotate}
      </button>
    </div>

    <div class="editor-workspace">
      <!-- Canvas Container -->
      <div class="canvas-container">
        {#if imageLoaded}
          <canvas
            bind:this={canvas}
            onmousedown={handleMouseDown}
            onmousemove={handleMouseMove}
            onmouseup={handleMouseUp}
            onmouseleave={handleMouseUp}
            class="editor-canvas"
            style:cursor={currentMode === "crop" ? "crosshair" : "default"}
          ></canvas>

          {#if currentMode === "crop"}
            <div class="crop-hint">
              {t.dragToSelect}
            </div>
          {/if}
        {:else}
          <div class="loading-placeholder">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        {/if}
      </div>

      <!-- Controls Panel -->
      <div class="controls-panel">
        {#if currentMode === "resize"}
          <div class="control-section">
            <h6>{t.resizeMode}</h6>
            <div class="row g-2">
              <div class="col-6">
                <label class="form-label" for="targetWidth">{t.width}</label>
                <input
                  id="targetWidth"
                  type="number"
                  class="form-control form-control-sm"
                  bind:value={targetWidth}
                  onchange={handleWidthChange}
                  min="1"
                  max="4000"
                />
              </div>
              <div class="col-6">
                <label class="form-label" for="targetHeight">{t.height}</label>
                <input
                  id="targetHeight"
                  type="number"
                  class="form-control form-control-sm"
                  bind:value={targetHeight}
                  onchange={handleHeightChange}
                  min="1"
                  max="4000"
                />
              </div>
            </div>
            <div class="form-check mt-2">
              <input
                class="form-check-input"
                type="checkbox"
                bind:checked={maintainAspectRatio}
                id="aspectRatio"
              />
              <label class="form-check-label" for="aspectRatio">
                {t.maintainAspectRatio}
              </label>
            </div>
          </div>
        {/if}

        {#if currentMode === "rotate"}
          <div class="control-section">
            <h6>{t.rotateMode}</h6>
            <div class="btn-group-vertical w-100" role="group">
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick={rotateLeft}>
                <i class="fas fa-undo"></i>
                {t.rotateLeft}
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick={rotateRight}>
                <i class="fas fa-redo"></i>
                {t.rotateRight}
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick={toggleFlipX}>
                <i class="fas fa-arrows-alt-h"></i>
                {t.flipHorizontal}
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick={toggleFlipY}>
                <i class="fas fa-arrows-alt-v"></i>
                {t.flipVertical}
              </button>
            </div>
          </div>
        {/if}

        {#if currentMode === "crop"}
          <div class="control-section">
            <h6>{t.cropMode}</h6>
            <div class="crop-info">
              <small class="text-muted">
                {Math.round(cropWidth / scale)} × {Math.round(cropHeight / scale)}px
              </small>
            </div>
          </div>
        {/if}

        <!-- Quality Control -->
        <div class="control-section">
          <label class="form-label" for="qualityRange"
            >{t.quality}: {Math.round(quality * 100)}%</label
          >
          <input
            id="qualityRange"
            type="range"
            class="form-range"
            min="0.1"
            max="1"
            step="0.1"
            bind:value={quality}
          />
        </div>

        <!-- Reset Button -->
        <button
          type="button"
          class="btn btn-outline-warning btn-sm w-100"
          onclick={resetTransforms}
        >
          <i class="fas fa-undo-alt"></i>
          {t.reset}
        </button>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="editor-footer">
    <button type="button" class="btn btn-secondary" onclick={onCancel} disabled={processing}>
      {t.cancel}
    </button>
    <button
      type="button"
      class="btn btn-primary"
      onclick={saveImage}
      disabled={processing || !imageLoaded}
    >
      {#if processing}
        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
        {t.processing}
      {:else}
        {t.save}
      {/if}
    </button>
  </div>
</div>

<style>
  .image-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 600px;
  }

  .editor-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
  }

  .editor-content {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
  }

  .mode-tabs {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
  }

  .editor-workspace {
    flex: 1;
    display: flex;
    gap: 1rem;
    min-height: 400px;
  }

  .canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    position: relative;
    min-height: 400px;
  }

  .editor-canvas {
    max-width: 100%;
    max-height: 100%;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .crop-hint {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    pointer-events: none;
  }

  .loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  .controls-panel {
    width: 280px;
    flex-shrink: 0;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: fit-content;
  }

  .control-section {
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
  }

  .control-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .control-section h6 {
    margin-bottom: 0.75rem;
    color: #495057;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .crop-info {
    text-align: center;
    padding: 0.5rem;
    background-color: white;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
  }

  .editor-footer {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }

  .btn-group-vertical .btn {
    margin-bottom: 0.5rem;
  }

  .btn-group-vertical .btn:last-child {
    margin-bottom: 0;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .editor-workspace {
      flex-direction: column;
    }

    .controls-panel {
      width: 100%;
      order: -1;
    }

    .canvas-container {
      min-height: 300px;
    }

    .mode-tabs {
      flex-wrap: wrap;
    }
  }

  /* Focus styles for accessibility */
  .editor-canvas:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  /* Smooth transitions */
  .btn {
    transition: all 0.15s ease-in-out;
  }

  .control-section {
    transition: border-color 0.15s ease-in-out;
  }
</style>
