<script lang="ts">
  import type { Common } from "@commune/api";

  import { Consts } from "@commune/api";
  import { fetchWithAuth } from "$lib";
  import { Modal, ImageEditor } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    userId: string;
    onImageUploaded: () => void;
  }

  const MAX_FILE_SIZE_MB = Consts.MAX_IMAGE_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      uploadImage: "Upload Profile Image",
      upload: "Upload",
      cancel: "Cancel",
      uploading: "Uploading...",
      imageUploadedSuccessfully: "Image uploaded successfully!",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Failed to upload image",
      errorOccurred: "An error occurred while uploading the image",
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
    },

    ru: {
      uploadImage: "Загрузить изображение профиля",
      upload: "Загрузить",
      cancel: "Отменить",
      uploading: "Загрузка...",
      imageUploadedSuccessfully: "Изображение загружено успешно!",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Не удалось загрузить изображение",
      errorOccurred: "Произошла ошибка при загрузке изображения",
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
    },
  };

  const { locale, show, onHide, userId, onImageUploaded }: Props = $props();

  const t = $derived(i18n[locale]);

  let selectedFile = $state<File | null>(null);
  let previewUrl = $state<string | null>(null);
  let error = $state("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);
  let showImageEditor = $state(false);
  let editedFile = $state<File | null>(null);

  const handleFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const files = target.files;

    error = "";

    if (!files || files.length === 0) {
      selectedFile = null;
      previewUrl = null;

      return;
    }

    const file = files[0];

    // Validate file type
    if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
      error = t.invalidFileTypeError;
      selectedFile = null;
      previewUrl = null;
      // Clear the input immediately if validation fails
      target.value = "";

      return;
    }

    // Validate file size
    if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
      error = t.fileTooLarge;
      selectedFile = null;
      previewUrl = null;
      // Clear the input immediately if validation fails
      target.value = "";

      return;
    }

    selectedFile = file;
    editedFile = null;
    showImageEditor = true;

    // Create preview URL for fallback
    const objectUrl = URL.createObjectURL(file);
    previewUrl = objectUrl;

    return () => {
      URL.revokeObjectURL(objectUrl);
    };
  };

  const handleImageEditorSave = (file: File) => {
    editedFile = file;
    showImageEditor = false;

    // Update preview with edited image
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    const objectUrl = URL.createObjectURL(file);
    previewUrl = objectUrl;
  };

  const handleImageEditorCancel = () => {
    showImageEditor = false;
  };

  const handleEditImage = () => {
    if (selectedFile) {
      showImageEditor = true;
    }
  };

  const handleSelectDifferentImage = () => {
    selectedFile = null;
    editedFile = null;
    previewUrl = null;
    showImageEditor = false;
    error = "";

    // Clear the file input
    const imageInput = document.getElementById("imageInput") as HTMLInputElement | null;
    if (imageInput) {
      imageInput.value = "";
    }
  };

  const handleSubmit = async () => {
    const fileToUpload = editedFile || selectedFile;

    if (!fileToUpload) {
      error = t.pleaseSelectImage;
      return;
    }

    isSubmitting = true;
    error = "";

    try {
      const formData = new FormData();
      formData.append("image", fileToUpload);

      const response = await fetchWithAuth(`/api/user/${userId}/image`, {
        method: "PUT",
        body: formData,
        // Don't set Content-Type header, it will be set automatically with the boundary
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUploadImage);
      }

      submitSuccess = true;
      onImageUploaded();

      const imageInput = document.getElementById("imageInput") as HTMLInputElement | null;

      if (imageInput) {
        imageInput.files = null;
      }

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    selectedFile = null;
    editedFile = null;
    previewUrl = null;
    error = "";
    submitSuccess = false;
    showImageEditor = false;
    onHide();
  };
</script>

<Modal
  {show}
  title={t.uploadImage}
  onClose={handleClose}
  onSubmit={showImageEditor ? undefined : handleSubmit}
  submitText={isSubmitting ? t.uploading : t.upload}
  cancelText={t.cancel}
  submitDisabled={showImageEditor || (!selectedFile && !editedFile) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
  size={showImageEditor ? "xl" : "lg"}
  showFooter={!showImageEditor}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.imageUploadedSuccessfully}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  {#if showImageEditor && selectedFile}
    <ImageEditor
      imageFile={selectedFile}
      onSave={handleImageEditorSave}
      onCancel={handleImageEditorCancel}
      {locale}
    />
  {:else}
    <form>
      <div class="mb-3">
        <label for="imageInput" class="form-label">{t.pleaseSelectImage}</label>
        <input
          id="imageInput"
          type="file"
          class="form-control"
          accept=".jpg,.jpeg,.png,.webp"
          onchange={handleFileChange}
          disabled={isSubmitting}
        />
        <p class="form-text text-muted">
          {t.uploadImageMaxSize}
        </p>

        {#if previewUrl && !showImageEditor}
          <div class="mt-3">
            <div class="text-center mb-3">
              <img src={previewUrl} alt="Preview" class="img-thumbnail" style:max-height="200px" />
            </div>

            <div class="d-flex gap-2 justify-content-center">
              <button
                type="button"
                class="btn btn-outline-primary btn-sm"
                onclick={handleEditImage}
                disabled={isSubmitting}
              >
                <i class="fas fa-edit"></i>
                {t.editImage}
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick={handleSelectDifferentImage}
                disabled={isSubmitting}
              >
                <i class="fas fa-image"></i>
                {t.selectDifferentImage}
              </button>
            </div>
          </div>
        {/if}
      </div>
    </form>
  {/if}
</Modal>
